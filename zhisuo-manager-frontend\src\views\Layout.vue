<template>
  <div class="layout-container">
    <!-- 顶部导航 -->
    <div class="layout-header">
      <div class="header-left">
        <div class="logo-section">
          <div class="logo-icon">
            <img src="@/assets/logo.png" alt="Logo" class="logo-image" />
          </div>
          <h1 class="system-title">智索管理平台</h1>
        </div>
      </div>

      <div class="header-center">
        <div class="search-box">
          <el-input
            placeholder="搜索..."
            prefix-icon="Search"
            v-model="searchKeyword"
            style="width: 300px;"
          />
        </div>
      </div>

      <div class="header-right">
        <div class="header-actions">
          <el-button type="text" class="action-btn">
            <el-icon size="18"><Bell /></el-icon>
          </el-button>
          <el-button type="text" class="action-btn">
            <el-icon size="18"><QuestionFilled /></el-icon>
          </el-button>
        </div>

        <el-dropdown @command="handleCommand" trigger="click" hide-on-click>
          <div class="user-info">
            <el-avatar :size="32" :src="userInfo?.avatar">
              <el-icon><User /></el-icon>
            </el-avatar>
            <span class="username">{{ userInfo?.realName || '管理员' }}</span>
            <el-icon class="arrow-down"><ArrowDown /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">
                <el-icon><User /></el-icon>
                个人信息
              </el-dropdown-item>
              <el-dropdown-item command="settings">
                <el-icon><Setting /></el-icon>
                系统设置
              </el-dropdown-item>
              <el-dropdown-item divided command="logout">
                <el-icon><SwitchButton /></el-icon>
                退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    
    <!-- 主体内容 -->
    <div class="layout-content">
      <!-- 侧边栏 -->
      <div class="layout-sidebar" :class="{ collapsed: isCollapsed }">
        <div class="sidebar-header">
          <el-button
            class="collapse-btn"
            @click="toggleSidebar"
            :icon="isCollapsed ? Expand : Fold"
            circle
            size="default"
          />
        </div>

        <el-menu
          :default-active="activeMenu"
          :unique-opened="true"
          router
          class="sidebar-menu"
          background-color="#FFFFFF"
          text-color="#6B7280"
          active-text-color="#FFFFFF"
          :collapse="isCollapsed"
          :collapse-transition="true"
        >
          <el-menu-item index="/dashboard" class="menu-item">
            <el-icon><TrendCharts /></el-icon>
            <template #title>数据概览</template>
          </el-menu-item>

          <el-menu-item index="/users" class="menu-item">
            <el-icon><Avatar /></el-icon>
            <template #title>用户管理</template>
          </el-menu-item>

          <el-menu-item index="/articles" class="menu-item">
            <el-icon><Management /></el-icon>
            <template #title>文章管理</template>
          </el-menu-item>

          <el-menu-item index="/topics" class="menu-item">
            <el-icon><Comment /></el-icon>
            <template #title>热点管理</template>
          </el-menu-item>

          <el-menu-item index="/statistics" class="menu-item">
            <el-icon><Histogram /></el-icon>
            <template #title>数据统计</template>
          </el-menu-item>

          <el-menu-item index="/personal-center" class="menu-item">
            <el-icon><UserFilled /></el-icon>
            <template #title>个人中心</template>
          </el-menu-item>

          <el-sub-menu index="/system" class="sub-menu">
            <template #title>
              <el-icon><Tools /></el-icon>
              <span>系统设置</span>
            </template>
            <el-menu-item index="/system/basic" class="sub-menu-item">
              <el-icon><Setting /></el-icon>
              <template #title>基础设置</template>
            </el-menu-item>
            <el-menu-item index="/system/admin" class="sub-menu-item">
              <el-icon><UserFilled /></el-icon>
              <template #title>管理员与权限</template>
            </el-menu-item>
            <el-menu-item index="/system/security" class="sub-menu-item">
              <el-icon><Lock /></el-icon>
              <template #title>安全配置</template>
            </el-menu-item>
            <el-menu-item index="/system/logs" class="sub-menu-item">
              <el-icon><Document /></el-icon>
              <template #title>系统日志</template>
            </el-menu-item>
          </el-sub-menu>
        </el-menu>
      </div>
      
      <!-- 主要内容区域 -->
      <div class="layout-main">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessageBox, ElMessage } from 'element-plus'
import {
  Expand,
  Fold,
  UserFilled,
  Tools,
  Bell,
  QuestionFilled,
  ArrowDown,
  SwitchButton,
  Avatar,
  Histogram,
  Setting,
  Lock,
  Document,
  TrendCharts,
  Management,
  Comment
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

const isCollapsed = ref(false)
const searchKeyword = ref('')

// 计算当前激活的菜单
const activeMenu = computed(() => {
  return route.path
})

// 用户信息
const userInfo = computed(() => authStore.userInfo)

// 切换侧边栏
const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value
}

// 处理下拉菜单命令
const handleCommand = async (command) => {
  switch (command) {
    case 'profile':
      router.push('/personal-center')
      break
    case 'settings':
      router.push('/system')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm(
          '确定要退出登录吗？',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        await authStore.logout()
        ElMessage.success('退出登录成功')
        router.push('/login')
      } catch (error) {
        // 用户取消操作
      }
      break
  }
}
</script>

<style lang="scss" scoped>
.layout-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.layout-header {
  height: 64px;
  background: #2D3748;
  border-bottom: 1px solid #4A5568;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  z-index: 1000;

  .header-left {
    .logo-section {
      display: flex;
      align-items: center;
      gap: 12px;

      .logo-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;

        .logo-image {
          width: 32px;
          height: 32px;
          object-fit: contain;
          border-radius: 6px;
        }
      }

      .system-title {
        font-size: 20px;
        font-weight: 700;
        color: #FFFFFF;
        margin: 0;
      }
    }
  }

  .header-center {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    padding-right: 30px;

    .search-box {
      :deep(.el-input) {
        .el-input__wrapper {
          border-radius: 8px;
          border: 1px solid #718096;
          box-shadow: none;
          background: #4A5568;

          &:hover {
            border-color: #8B5CF6;
          }

          &.is-focus {
            border-color: #8B5CF6;
            box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.1);
          }
        }

        .el-input__inner {
          color: #FFFFFF;

          &::placeholder {
            color: #A0AEC0;
          }
        }

        .el-input__prefix-inner {
          color: #A0AEC0;
        }
      }
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 16px;

    .header-actions {
      display: flex;
      gap: 8px;

      .action-btn {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #A0AEC0;

        &:hover {
          background: #4A5568;
          color: #FFFFFF;
        }
      }
    }

    .user-info {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 8px 16px;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.2s ease-in-out;
      position: relative;

      &:hover {
        background: #4A5568;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }

      &:active {
        transform: translateY(0);
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
      }

      .username {
        font-size: 14px;
        font-weight: 500;
        color: #FFFFFF;
        transition: color 0.2s ease-in-out;
      }

      .arrow-down {
        font-size: 12px;
        color: #A0AEC0;
        transition: all 0.2s ease-in-out;
      }

      &:hover .arrow-down {
        color: #FFFFFF;
        transform: rotate(180deg);
      }
    }
  }
}

.layout-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.layout-sidebar {
  width: 220px;
  background: #FFFFFF;
  transition: width 0.25s ease-out, box-shadow 0.25s ease-out;
  position: relative;
  z-index: 100;
  overflow: hidden;
  border-right: 1px solid #E5E7EB;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05);

  &.collapsed {
    width: 56px;
    box-shadow: 4px 0 12px rgba(0, 0, 0, 0.08);

    .sidebar-header {
      padding: 16px 4px;
      justify-content: center;
    }
  }

  .sidebar-header {
    padding: 16px 12px;
    border-bottom: 1px solid #E5E7EB;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    min-height: 64px;

    .collapse-btn {
      background: #F3F4F6;
      border: none;
      color: #6B7280;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      width: 36px;
      height: 36px;
      font-size: 16px;

      &:hover {
        background: #8B5CF6;
        color: #FFFFFF;
        transform: scale(1.1) rotate(180deg);
        box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
      }

      &:active {
        transform: scale(0.95);
      }

      :deep(.el-icon) {
        font-size: 16px;
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }
    }
  }

  .sidebar-menu {
    border: none;
    height: calc(100vh - 64px);
    background: #FFFFFF;
    overflow: hidden;

    :deep(.el-menu-item) {
      height: 48px;
      line-height: 48px;
      margin: 4px 8px;
      border-radius: 8px;
      transition: background-color 0.2s ease-out, transform 0.2s ease-out;
      display: flex;
      align-items: center;
      will-change: transform, background-color;

      &:hover {
        background-color: #F3F4F6 !important;
        color: #374151 !important;
        transform: translateX(2px);
      }

      &.is-active {
        background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%) !important;
        color: #FFFFFF !important;
        transform: translateX(2px);

        .el-icon {
          color: #FFFFFF !important;
        }
      }

      .el-icon {
        color: #6B7280;
        font-size: 18px;
        margin-right: 12px;
        transition: color 0.2s ease-out;
      }
    }

    // 收缩状态下的样式
    &.el-menu--collapse {
      :deep(.el-menu-item) {
        margin: 4px 6px !important;
        width: 44px !important;
        min-width: 44px !important;
        padding: 0 !important;
        justify-content: center !important;

        &:hover {
          background-color: #F3F4F6 !important;
        }

        .el-icon {
          margin-right: 0 !important;
          margin-left: 0 !important;
        }
      }

      :deep(.el-sub-menu__title) {
        margin: 4px 6px !important;
        width: 44px !important;
        min-width: 44px !important;
        padding: 0 !important;
        justify-content: center !important;
        display: flex !important;
        align-items: center !important;

        .el-icon {
          margin-right: 0 !important;
          margin-left: 0 !important;
        }

        .el-sub-menu__icon-arrow {
          display: none !important;
        }
      }

      :deep(.el-tooltip__trigger) {
        width: 100% !important;
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
      }
    }

    :deep(.el-sub-menu__title) {
      height: 48px;
      line-height: 48px;
      margin: 4px 8px;
      border-radius: 8px;
      transition: background-color 0.2s ease-out, transform 0.2s ease-out;

      &:hover {
        background-color: #F3F4F6;
        color: #374151;
        transform: translateX(2px);
      }

      .el-icon {
        color: #6B7280;
        font-size: 18px;
        margin-right: 12px;
        transition: color 0.2s ease-out;
      }
    }

    :deep(.el-sub-menu) {
      .el-menu-item {
        height: 40px;
        line-height: 40px;
        margin: 2px 8px 2px 16px;
        padding-left: 24px !important;
        border-radius: 6px;
        font-size: 13px;

        &:hover {
          background-color: #F3F4F6 !important;
          color: #374151 !important;
          transform: translateX(2px);
        }

        &.is-active {
          background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%) !important;
          color: #FFFFFF !important;
          transform: translateX(2px);

          .el-icon {
            color: #FFFFFF !important;
          }
        }

        .el-icon {
          color: #6B7280;
          font-size: 14px;
          margin-right: 6px;
          transition: color 0.2s ease-out;
        }
      }
    }
  }
}

.layout-main {
  flex: 1;
  background: #F9FAFB;
  overflow-y: auto;
  position: relative;
  z-index: 1;
}

// 下拉菜单样式优化
:deep(.el-dropdown) {
  .el-dropdown-menu {
    border: 1px solid #E5E7EB;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 4px 0;
    margin-top: 8px;

    .el-dropdown-menu__item {
      padding: 8px 16px;
      font-size: 14px;
      transition: all 0.2s ease-in-out;
      display: flex;
      align-items: center;
      gap: 8px;

      &:hover {
        background: #F3F4F6;
        color: #374151;
      }

      .el-icon {
        font-size: 16px;
      }
    }
  }
}

@media (max-width: 768px) {
  .layout-sidebar {
    position: fixed;
    left: 0;
    top: 60px;
    height: calc(100vh - 60px);
    z-index: 999;
    transform: translateX(-100%);
    transition: transform 0.3s;

    &:not(.collapsed) {
      transform: translateX(0);
    }
  }

  .layout-main {
    padding: 10px;
  }

  .header-left .system-title {
    display: none;
  }
}
</style>
